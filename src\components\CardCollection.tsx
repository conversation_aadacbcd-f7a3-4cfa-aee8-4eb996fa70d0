import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import './CardCollection.css';

interface CardCollectionContextType {
  collectedCards: string[];
  toggleCollection: (cardId: string) => void;
  openCollection: () => void;
  closeCollection: () => void;
  isCollectionOpen: boolean;
}

const CardCollectionContext = createContext<CardCollectionContextType | undefined>(undefined);

export const CardCollectionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // 从本地存储加载收藏的卡片
  const [collectedCards, setCollectedCards] = useState<string[]>(() => {
    const saved = localStorage.getItem('collectedPokemonCards');
    return saved ? JSON.parse(saved) : [];
  });
  const [isCollectionOpen, setIsCollectionOpen] = useState(false);

  // 当收藏卡片变化时保存到本地存储
  useEffect(() => {
    localStorage.setItem('collectedPokemonCards', JSON.stringify(collectedCards));
  }, [collectedCards]);

  // 切换卡片收藏状态
  const toggleCollection = (cardId: string) => {
    setCollectedCards(prev => 
      prev.includes(cardId)
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  const openCollection = () => setIsCollectionOpen(true);
  const closeCollection = () => setIsCollectionOpen(false);

  return (
    <CardCollectionContext.Provider value={{
      collectedCards,
      toggleCollection,
      openCollection,
      closeCollection,
      isCollectionOpen
    }}>
      {children}
    </CardCollectionContext.Provider>
  );
};

// 自定义Hook，供其他组件使用收藏功能
export const useCardCollection = () => {
  const context = useContext(CardCollectionContext);
  if (context === undefined) {
    throw new Error('useCardCollection must be used within a CardCollectionProvider');
  }
  return context;
};

// 收藏按钮组件
export const CollectionButton: React.FC = () => {
  const { openCollection, collectedCards } = useCardCollection();
  return (
    <button onClick={openCollection} className="collection-button">
      收藏夹 ({collectedCards.length})
    </button>
  );
};

// 卡片包组件
export const CardPack: React.FC = () => {
  return (
    <div className="card-pack">
      <div className="pack-top"></div>
      <div className="pack-middle">
        <p>宝可梦卡片包</p>
      </div>
      <div className="pack-bottom"></div>
    </div>
  );
};

// 收藏视图组件
export const CardCollectionView: React.FC = () => {
  const { collectedCards, closeCollection, isCollectionOpen } = useCardCollection();
  import pokemonCards from '../pokemon-cards.json';
  import PokemonCard from './PokemonCard';

  // 获取已收藏的卡片详情
  const getCollectedCardDetails = () => {
    return pokemonCards.cards.filter(card => 
      collectedCards.includes(card.id)
    );
  };

  return (
    <div className={`collection-view ${isCollectionOpen ? 'open' : ''}`}>
      <div className="collection-header">
        <h2>我的宝可梦收藏</h2>
        <button onClick={closeCollection} className="close-btn">×</button>
      </div>
      <div className="collection-content">
        {collectedCards.length === 0 ? (
          <div className="empty-collection">
            <p>你的收藏还是空的</p>
            <p>答对单词释义问题来收集卡片吧！</p>
          </div>
        ) : (
          <div className="cards-grid">
            {getCollectedCardDetails().map(card => (
              <PokemonCard
                key={card.id}
                card={card}
                isCollected={true}
                onCollect={() => {}}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};