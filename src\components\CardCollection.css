.collection-button {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #2d5b99;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
  z-index: 1000;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.collection-button:hover {
  background-color: #1a4b8c;
}

.card-pack {
  width: 180px;
  height: 250px;
  margin: 20px auto;
  position: relative;
  cursor: pointer;
}

.pack-top {
  height: 30px;
  background-color: #e74c3c;
  border-radius: 10px 10px 0 0;
}

.pack-middle {
  height: 190px;
  background-color: #c0392b;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
}

.pack-bottom {
  height: 30px;
  background-color: #e74c3c;
  border-radius: 0 0 10px 10px;
}

.collection-view {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.8);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.collection-view.open {
  display: flex;
}

.collection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  color: white;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
}

.collection-content {
  background-color: #2c3e50;
  padding: 20px;
  border-radius: 10px;
  max-width: 90%;
  max-height: 80%;
  overflow-y: auto;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

.empty-collection {
  color: white;
  text-align: center;
  padding: 40px;
}

@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}

@media (max-width: 480px) {
  .cards-grid {
    grid-template-columns: 1fr;
  }
}