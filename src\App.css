#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

:root {
  --primary-color: #4f46e5;
  --secondary-color: #7c3aed;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --error-color: #ef4444;
  --text-color: #1f2937;
  --bg-color: #f9fafb;
  --card-bg: #ffffff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Arial Rounded MT Bold', 'Helvetica Rounded', Arial, sans-serif;
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
  padding: 20px;
  line-height: 1.6;
}

.app-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 30px;
  font-size: 2.5rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.score-container {
  text-align: right;
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--secondary-color);
}

.question-container {
  background-color: var(--card-bg);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin-bottom: 30px;
}

.word-display {
  background-color: var(--primary-color);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  margin: 20px 0;
  font-size: 2rem;
  font-weight: bold;
}

.answer-form {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

.answer-form input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.answer-form input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.answer-form button {
  padding: 12px 24px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: background-color 0.3s;
}

.answer-form button:hover {
  background-color: #4338ca;
}

.feedback {
  margin-top: 15px;
  padding: 12px;
  border-radius: 8px;
  font-weight: bold;
}

.incorrect {
  background-color: #fee2e2;
  color: var(--error-color);
}

.card-container {
  background-color: var(--card-bg);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  text-align: center;
}

.pokemon-card {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  display: inline-block;
  min-width: 300px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.pokemon-card h3 {
  color: var(--primary-color);
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.attacks {
  margin-top: 15px;
  text-align: left;
}

.attack {
  background-color: rgba(255, 255, 255, 0.7);
  padding: 8px 12px;
  border-radius: 6px;
  margin-top: 8px;
}

@media (max-width: 600px) {
  .answer-form {
    flex-direction: column;
  }

  .pokemon-card {
    min-width: 0;
    width: 100%;
  }
}

.collection-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  padding: 20px;
  z-index: 1000;
}

.collection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  color: white;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
}

.collection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  overflow-y: auto;
  flex-grow: 1;
}

.collection-card {
  background-color: white;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.collection-card-image {
  width: 100px;
  height: 150px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  border-radius: 4px;
  font-weight: bold;
}

.collection-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 24px;
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.card-pack-container {
  background-color: var(--card-bg);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 30px;
  text-align: center;
}

.draw-card-button {
  padding: 15px 30px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  margin-top: 10px;
}

.drawn-card {
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
}

.collect-now-button {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: var(--success-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.remove-collection-button {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: var(--error-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}