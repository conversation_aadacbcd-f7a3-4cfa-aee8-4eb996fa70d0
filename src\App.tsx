import React, { useState, useEffect } from 'react';
import vocabularyData from './vocabulary.json';
import pokemonCards from './pokemon-cards.json';
import { useCardCollection } from './components/CardCollection';
import { CardCollectionProvider, CollectionButton, CardPack, CardCollectionView } from './components/CardCollection';
import PokemonCard from './components/PokemonCard';
import './App.css';

const App: React.FC = () => {
  return (
    <CardCollectionProvider>
      <AppContent />
    </CardCollectionProvider>
  );
};

const AppContent: React.FC = () => {
  const { toggleCollection, collectedCards } = useCardCollection();
  const [currentWord, setCurrentWord] = useState<any>(null);
  const [userAnswer, setUserAnswer] = useState('');
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [showCard, setShowCard] = useState(false);
  const [drawnCard, setDrawnCard] = useState<any>(null);
  const [score, setScore] = useState(0);

  // Function to get random word
  const getRandomWord = () => {
    const randomIndex = Math.floor(Math.random() * vocabularyData.words.length);
    return vocabularyData.words[randomIndex];
  };

  // Function to get random Pokémon card
  const getRandomPokemonCard = () => {
    const randomIndex = Math.floor(Math.random() * pokemonCards.cards.length);
    return pokemonCards.cards[randomIndex];
  };

  // Initialize with a random word
  useEffect(() => {
    setCurrentWord(getRandomWord());
  }, []);

  // Handle answer submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Check if the answer contains any part of the definition
    const isAnswerCorrect = vocabularyData.words.some(word => 
      word.id === currentWord.id && 
      word.definition.toLowerCase().includes(userAnswer.toLowerCase())
    );

    setIsCorrect(isAnswerCorrect);

    if (isAnswerCorrect) {
      setScore(prev => prev + 1);
      // Draw a card for correct answer
      setDrawnCard(getRandomPokemonCard());
      setShowCard(true);
      // Get new word after short delay
      setTimeout(() => {
        setCurrentWord(getRandomWord());
        setUserAnswer('');
        setIsCorrect(null);
        setShowCard(false);
      }, 3000);
    }
  };

  if (!currentWord) return <div>Loading...</div>;

  return (
    <div className="app-container">
      <CollectionButton />
      <CardPack />
      <CardCollectionView />
      <h1>词汇学习宝可梦</h1>
      <div className="score-container">
        <p>得分: {score}</p>
      </div>

      {showCard && drawnCard ? (
        <div className="card-container">
          <h2>恭喜你答对了！获得宝可梦卡片：</h2>
          <PokemonCard 
            card={drawnCard} 
            isCollected={collectedCards.includes(drawnCard.id)} 
            onCollect={toggleCollection} 
          />
        </div>
      ) : (
        <div className="question-container">
          <h2>请写出以下单词的释义:</h2>
          <div className="word-display">
            <h3>{currentWord.word}</h3>
          </div>
          <form onSubmit={handleSubmit} className="answer-form">
            <input
              type="text"
              value={userAnswer}
              onChange={(e) => setUserAnswer(e.target.value)}
              placeholder="输入单词释义..."
              required
            />
            <button type="submit">提交答案</button>
          </form>
          {isCorrect === false && (
            <div className="feedback incorrect">
              不正确，请再试一次！正确答案：{currentWord.definition}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default App