.pokemon-card {
  width: 280px;
  border: 8px solid #e0e0e0;
  border-radius: 12px;
  background-color: white;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 10px;
  margin: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.card-name {
  margin: 0;
  font-size: 1.4rem;
  color: #333;
}

.card-type {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-weight: bold;
}

.card-type svg {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.card-image {
  width: 100%;
  height: 180px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 10px;
}

.card-image img {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

.card-stats {
  margin-bottom: 10px;
  font-weight: bold;
}

.hp-stat {
  color: #ff3333;
  font-size: 0.9rem;
}

.card-moves {
  margin-bottom: 15px;
}

.move {
  background-color: #f0f0f0;
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 8px;
}

.move-name {
  font-weight: bold;
  color: #2d5b99;
  margin-bottom: 4px;
}

.move-description {
  font-size: 0.8rem;
  color: #555;
  margin-bottom: 4px;
}

.move-damage {
  font-size: 0.8rem;
  color: #d1462f;
  text-align: right;
}

.card-actions {
  display: flex;
  justify-content: center;
}

.collect-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.collect-btn:hover {
  background-color: #45a049;
}

.collected-btn {
  background-color: #cccccc;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: not-allowed;
  font-weight: bold;
}