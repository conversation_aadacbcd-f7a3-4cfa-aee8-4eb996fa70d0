/* 宝可梦卡片主容器 - 模拟真实TCG卡片 */
.pokemon-card {
  width: 300px;
  height: 420px;
  border: 3px solid #8B8B8B; /* 现代TCG灰色边框 */
  border-radius: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  padding: 12px;
  margin: 15px;
  position: relative;
  overflow: hidden;
  font-family: 'Arial', sans-serif;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pokemon-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.2),
    0 8px 15px rgba(0, 0, 0, 0.15);
}

/* 稀有卡片全息效果 */
.pokemon-card.rare {
  border: 3px solid transparent;
  background:
    linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%),
    linear-gradient(45deg, #FFD700, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
  background-clip: padding-box, border-box;
  animation: holographic 3s ease-in-out infinite;
}

@keyframes holographic {
  0%, 100% { filter: hue-rotate(0deg) brightness(1); }
  25% { filter: hue-rotate(90deg) brightness(1.1); }
  50% { filter: hue-rotate(180deg) brightness(1.2); }
  75% { filter: hue-rotate(270deg) brightness(1.1); }
}

/* 卡片头部 - 名称和HP */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 0 4px;
}

.card-name {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  flex: 1;
}

.card-hp {
  font-size: 1.2rem;
  font-weight: bold;
  color: #e74c3c;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  padding: 4px 8px;
  border-radius: 8px;
  border: 2px solid #e74c3c;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 属性类型标签 */
.card-type {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
  margin: 8px 0;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.card-type svg {
  width: 18px;
  height: 18px;
  margin-right: 6px;
  filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, 0.3));
}

/* 卡片图片区域 */
.card-image {
  width: 100%;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
  border: 2px solid #dadce0;
  border-radius: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  position: relative;
}

.card-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%);
  pointer-events: none;
}

.card-image img {
  max-width: 95%;
  max-height: 95%;
  object-fit: contain;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.2));
}

/* 技能区域 */
.card-moves {
  margin-bottom: 12px;
  max-height: 120px;
  overflow-y: auto;
}

.move {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 8px 10px;
  margin-bottom: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.move-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.move-name {
  font-weight: bold;
  color: #2c3e50;
  font-size: 0.95rem;
}

.move-damage {
  font-weight: bold;
  color: #e74c3c;
  font-size: 0.9rem;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e74c3c;
}

.move-description {
  font-size: 0.8rem;
  color: #6c757d;
  line-height: 1.3;
  margin-top: 4px;
}

/* 卡片底部信息 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px solid #dee2e6;
}

.card-rarity {
  font-size: 0.8rem;
  color: #6c757d;
  font-weight: bold;
}

/* 收集按钮 */
.card-actions {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.collect-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
}

.collect-btn:hover {
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(40, 167, 69, 0.4);
}

.collected-btn {
  background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: not-allowed;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}