/* 宝可梦卡片主容器 - 模拟真实TCG卡片 */
.pokemon-card {
  width: 300px;
  height: 420px;
  border: 3px solid #8B8B8B; /* 现代TCG灰色边框 */
  border-radius: 15px;
  background:
    /* 纸质纹理 */
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    /* 主背景 */
    linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%),
    /* 微妙的纹理 */
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.03) 2px,
      rgba(255, 255, 255, 0.03) 4px
    );
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
  padding: 12px;
  margin: 15px;
  position: relative;
  overflow: hidden;
  font-family: 'Arial', 'Microsoft YaHei', sans-serif;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.pokemon-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
  border-radius: 12px;
}

.pokemon-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.2),
    0 8px 15px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 稀有卡片全息效果 */
.pokemon-card.rare {
  border: 3px solid transparent;
  background:
    linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%),
    linear-gradient(45deg, #FFD700, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #DDA0DD, #98FB98);
  background-clip: padding-box, border-box;
  animation: holographic 4s ease-in-out infinite;
  position: relative;
}

/* 稀有卡片的图片区域特殊效果 */
.pokemon-card.rare .card-image {
  background:
    /* 全息光效 */
    radial-gradient(ellipse at 30% 30%, rgba(255, 215, 0, 0.6) 0%, transparent 50%),
    radial-gradient(ellipse at 70% 70%, rgba(255, 105, 180, 0.4) 0%, transparent 50%),
    radial-gradient(ellipse at 50% 10%, rgba(0, 255, 255, 0.3) 0%, transparent 60%),
    /* 彩虹渐变背景 */
    linear-gradient(45deg, #FFD700, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
  animation: rareImageShimmer 3s ease-in-out infinite;
}

.pokemon-card.rare::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #FFD700, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
  border-radius: 17px;
  z-index: -1;
  animation: borderShine 3s linear infinite;
}

@keyframes holographic {
  0%, 100% {
    filter: hue-rotate(0deg) brightness(1) saturate(1);
    transform: scale(1);
  }
  25% {
    filter: hue-rotate(90deg) brightness(1.05) saturate(1.1);
    transform: scale(1.01);
  }
  50% {
    filter: hue-rotate(180deg) brightness(1.1) saturate(1.2);
    transform: scale(1.02);
  }
  75% {
    filter: hue-rotate(270deg) brightness(1.05) saturate(1.1);
    transform: scale(1.01);
  }
}

@keyframes borderShine {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes rareImageShimmer {
  0%, 100% {
    background:
      radial-gradient(ellipse at 30% 30%, rgba(255, 215, 0, 0.4) 0%, transparent 50%),
      radial-gradient(ellipse at 70% 70%, rgba(255, 105, 180, 0.3) 0%, transparent 50%),
      linear-gradient(45deg, #FFD700, #FF6B6B, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7);
  }
  33% {
    background:
      radial-gradient(ellipse at 70% 30%, rgba(0, 255, 255, 0.5) 0%, transparent 50%),
      radial-gradient(ellipse at 30% 70%, rgba(255, 215, 0, 0.4) 0%, transparent 50%),
      linear-gradient(45deg, #4ECDC4, #45B7D1, #96CEB4, #FFEAA7, #FFD700, #FF6B6B);
  }
  66% {
    background:
      radial-gradient(ellipse at 50% 70%, rgba(255, 105, 180, 0.5) 0%, transparent 50%),
      radial-gradient(ellipse at 50% 30%, rgba(0, 255, 255, 0.3) 0%, transparent 50%),
      linear-gradient(45deg, #96CEB4, #FFEAA7, #FFD700, #FF6B6B, #4ECDC4, #45B7D1);
  }
}

/* 卡片头部 - 名称和HP */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 0 4px;
  position: relative;
}

.card-name {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 900;
  color: #2c3e50;
  text-shadow:
    1px 1px 2px rgba(0, 0, 0, 0.1),
    0 0 4px rgba(255, 255, 255, 0.8);
  flex: 1;
  letter-spacing: 0.5px;
  font-family: 'Arial Black', 'Microsoft YaHei', sans-serif;
}

.card-hp {
  font-size: 1.1rem;
  font-weight: 900;
  color: #fff;
  background:
    linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  padding: 6px 10px;
  border-radius: 12px;
  border: 2px solid #fff;
  box-shadow:
    0 3px 6px rgba(231, 76, 60, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  position: relative;
}

.card-hp::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #e74c3c, #f39c12, #e74c3c);
  border-radius: 13px;
  z-index: -1;
}

/* 属性类型标签 */
.card-type {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
  margin: 8px 0;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.card-type svg {
  width: 18px;
  height: 18px;
  margin-right: 6px;
  filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, 0.3));
}

/* 卡片图片区域 - 模拟真实TCG卡片的图片框 */
.card-image {
  width: 100%;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
  background:
    /* 动态光效背景 - 模拟TCG卡片的能量光效 */
    radial-gradient(ellipse at 20% 20%, rgba(255, 255, 255, 0.8) 0%, transparent 40%),
    radial-gradient(ellipse at 80% 80%, rgba(135, 206, 250, 0.4) 0%, transparent 40%),
    radial-gradient(ellipse at 50% 10%, rgba(255, 215, 0, 0.3) 0%, transparent 60%),
    /* 主背景 - 类似真实卡片的蓝色渐变 */
    linear-gradient(135deg, #e8f4fd 0%, #b3d9ff 30%, #87ceeb 70%, #4682b4 100%);
  border: 4px solid #fff;
  border-radius: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  position: relative;
  box-shadow:
    /* 外阴影 */
    0 4px 8px rgba(0, 0, 0, 0.15),
    /* 内阴影创造深度 */
    inset 0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 0 20px rgba(255, 255, 255, 0.6),
    /* 边框光泽 */
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* 图片区域光效 - 模拟TCG卡片的全息效果 */
.card-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg,
      transparent 20%,
      rgba(255, 255, 255, 0.4) 30%,
      rgba(135, 206, 250, 0.3) 50%,
      rgba(255, 255, 255, 0.4) 70%,
      transparent 80%);
  pointer-events: none;
  animation: tcgShimmer 4s ease-in-out infinite;
  border-radius: 9px;
}

/* 内边框效果 */
.card-image::after {
  content: '';
  position: absolute;
  top: 6px;
  left: 6px;
  right: 6px;
  bottom: 6px;
  border: 1px solid rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  pointer-events: none;
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.3);
}

.card-image img {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
  filter:
    drop-shadow(2px 2px 6px rgba(0, 0, 0, 0.3))
    drop-shadow(0 0 10px rgba(255, 255, 255, 0.2));
  transition: transform 0.3s ease, filter 0.3s ease;
  z-index: 2;
  position: relative;
}

.pokemon-card:hover .card-image img {
  transform: scale(1.08);
  filter:
    drop-shadow(3px 3px 8px rgba(0, 0, 0, 0.4))
    drop-shadow(0 0 15px rgba(255, 255, 255, 0.4));
}

/* TCG风格的闪光动画 */
@keyframes tcgShimmer {
  0%, 100% {
    background: linear-gradient(45deg,
      transparent 20%,
      rgba(255, 255, 255, 0.2) 30%,
      rgba(135, 206, 250, 0.2) 50%,
      rgba(255, 255, 255, 0.2) 70%,
      transparent 80%);
    transform: translateX(-100%);
  }
  50% {
    background: linear-gradient(45deg,
      transparent 10%,
      rgba(255, 255, 255, 0.6) 30%,
      rgba(135, 206, 250, 0.5) 50%,
      rgba(255, 255, 255, 0.6) 70%,
      transparent 90%);
    transform: translateX(100%);
  }
}

/* 技能区域 */
.card-moves {
  margin-bottom: 12px;
  max-height: 120px;
  overflow-y: auto;
  padding: 2px;
}

.card-moves::-webkit-scrollbar {
  width: 4px;
}

.card-moves::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.card-moves::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.move {
  background:
    linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 1px,
      rgba(0, 0, 0, 0.02) 1px,
      rgba(0, 0, 0, 0.02) 2px
    );
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 8px 10px;
  margin-bottom: 6px;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  transition: all 0.2s ease;
}

.move:hover {
  transform: translateY(-1px);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.move-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.move-name {
  font-weight: 900;
  color: #2c3e50;
  font-size: 0.95rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  font-family: 'Arial Black', 'Microsoft YaHei', sans-serif;
}

.move-damage {
  font-weight: 900;
  color: #fff;
  font-size: 0.85rem;
  background:
    linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  padding: 3px 8px;
  border-radius: 12px;
  border: 1px solid #fff;
  box-shadow:
    0 2px 4px rgba(231, 76, 60, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  min-width: 30px;
  text-align: center;
}

.move-description {
  font-size: 0.75rem;
  color: #6c757d;
  line-height: 1.4;
  margin-top: 4px;
  font-style: italic;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
}

/* 卡片底部信息 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px solid rgba(222, 226, 230, 0.5);
  position: relative;
}

.card-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.8) 50%,
    transparent 100%);
}

.card-rarity {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 2px 6px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 249, 250, 0.9) 100%);
  border-radius: 8px;
  border: 1px solid rgba(222, 226, 230, 0.8);
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
}

/* 稀有度特殊样式 */
.card-rarity.rare {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #8B4513;
  border-color: #DAA520;
  animation: rarityGlow 2s ease-in-out infinite alternate;
}

.card-rarity.ultra-rare {
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 100%);
  color: white;
  border-color: #FF6B6B;
  animation: rarityGlow 1.5s ease-in-out infinite alternate;
}

@keyframes rarityGlow {
  0% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
    transform: scale(1.05);
  }
}

/* 收集按钮 */
.card-actions {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.collect-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
}

.collect-btn:hover {
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(40, 167, 69, 0.4);
}

.collected-btn {
  background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: not-allowed;
  font-weight: bold;
  font-size: 0.9rem;
  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}