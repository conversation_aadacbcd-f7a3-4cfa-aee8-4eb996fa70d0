import React from 'react';
import './PokemonCard.css';

interface PokemonCardProps {
  card: {
    id: string | number;
    name: string;
    imageUrl?: string;
    image?: string;
    type: string;
    hp: number;
    stage?: string;
    attacks?: Array<{
      name: string;
      text?: string;
      damage: number;
      cost?: string[];
    }>;
    moves?: Array<{
      name: string;
      description: string;
      damage: number;
    }>;
    weaknesses?: Array<{type: string; value: string}>;
    resistances?: Array<{type: string; value: string}>;
    retreatCost?: string[];
    rarity?: string;
    description?: string;
  };
  isCollected: boolean;
  onCollect: () => void;
}

const PokemonCard: React.FC<PokemonCardProps> = ({ card, isCollected, onCollect }) => {
  // Define type-based colors and icons
  const typeColors: Record<string, string> = {
    fire: '#FF9C54',
    water: '#4A90DA',
    grass: '#63BB5B',
    electric: '#F3D250',
    psychic: '#F87C7C',
    fighting: '#CE4069',
    dark: '#5A5465',
    steel: '#BBBBBB',
    fairy: '#EC8FE6',
    normal: '#9099A1',
    flying: '#92AADE',
    poison: '#AB6AC8',
    ground: '#D97745',
    rock: '#C7B78B',
    bug: '#90C12C',
    ghost: '#5269AD',
    dragon: '#0B6DC3',
    ice: '#73CEC0',
    '电': '#F3D250',
    '水': '#4A90DA',
    '草': '#63BB5B',
    '火': '#FF9C54',
    '超能力': '#F87C7C',
    '格斗': '#CE4069',
    '恶': '#5A5465',
    '钢': '#BBBBBB',
    '妖精': '#EC8FE6',
    '一般': '#9099A1',
    '飞行': '#92AADE',
    '毒': '#AB6AC8',
    '地面': '#D97745',
    '岩石': '#C7B78B',
    '虫': '#90C12C',
    '幽灵': '#5269AD',
    '龙': '#0B6DC3',
    '冰': '#73CEC0'
  };

  // Get type icon SVG
  const getTypeIcon = (type: string) => {
    const typeLower = type.toLowerCase();
    switch(typeLower) {
      case 'fire':
      case '火':
        return <svg viewBox="0 0 24 24" width="18" height="18"><path fill="currentColor" d="M12,18c-2.76,0-5-2.24-5-5c0-2.76,2.24-5,5-5c2.76,0,5,2.24,5,5C17,15.76,14.76,18,12,18z M12,8c-2.21,0-4,1.79-4,4c0,2.21,1.79,4,4,4c2.21,0,4-1.79,4-4C16,9.79,14.21,8,12,8z M12,10c1.1,0,2,0.9,2,2s-0.9,2-2,2s-2-0.9-2-2S10.9,10,12,10z"/></svg>;
      case 'water':
      case '水':
        return <svg viewBox="0 0 24 24" width="18" height="18"><path fill="currentColor" d="M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9S16.97,3,12,3z M12,19c-3.87,0-7-3.13-7-7s3.13-7,7-7s7,3.13,7,7S15.87,19,12,19z M12,15c-1.1,0-2-0.9-2-2s0.9-2,2-2s2,0.9,2,2S13.1,15,12,15z"/></svg>;
      case 'grass':
      case '草':
        return <svg viewBox="0 0 24 24" width="18" height="18"><path fill="currentColor" d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M12,20c-4.41,0-8-3.59-8-8s3.59-8,8-8s8,3.59,8,8S16.41,20,12,20z M12,13c-0.55,0-1-0.45-1-1V8c0-0.55,0.45-1,1-1s1,0.45,1,1v4C13,12.55,12.55,13,12,13z M13,16h-2v-1h2V16z M13,18h-2v-1h2V18z"/></svg>;
      case 'electric':
      case '电':
        return <svg viewBox="0 0 24 24" width="18" height="18"><path fill="currentColor" d="M11,9V2L5,11h4v9l6-9H11z"/></svg>;
      default:
        return <span style={{fontSize: '12px'}}>⚡</span>;
    }
  };

  // Get the appropriate color for the card type
  const getTypeColor = (type: string): string => {
    return typeColors[type.toLowerCase()] || typeColors[type] || '#9099A1';
  };

  // Determine if this is a rare card (for holographic effect)
  const isRareCard = card.rarity === 'rare' || card.rarity === 'ultra rare' || card.rarity === 'secret rare';

  // Get the image URL - use Pokemon images from PokeAPI or fallback
  const getPokemonImageUrl = (id: string | number): string => {
    const pokemonId = typeof id === 'number' ? id : parseInt(id.toString()) || 1;
    // Use official Pokemon artwork from PokeAPI
    return `https://raw.githubusercontent.com/PokeAPI/sprites/master/sprites/pokemon/other/official-artwork/${pokemonId}.png`;
  };

  const imageUrl = card.imageUrl || getPokemonImageUrl(card.id);

  // Get moves/attacks data
  const moves = card.moves || card.attacks || [];

  return (
    <div className={`pokemon-card ${isRareCard ? 'rare' : ''}`}>
      <div className="card-header">
        <h3 className="card-name">{card.name}</h3>
        <div className="card-hp">HP {card.hp}</div>
      </div>

      <div className="card-type" style={{ backgroundColor: getTypeColor(card.type) }}>
        {getTypeIcon(card.type)}
        <span>{card.type}</span>
      </div>

      <div className="card-image">
        <img
          src={imageUrl}
          alt={card.name}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = `https://via.placeholder.com/200x150/f0f0f0/666?text=${encodeURIComponent(card.name)}`;
          }}
        />
      </div>

      <div className="card-moves">
        {moves.map((move, index) => (
          <div key={index} className="move">
            <div className="move-header">
              <div className="move-name">{move.name}</div>
              <div className="move-damage">{move.damage}</div>
            </div>
            <div className="move-description">
              {(move as any).description || (move as any).text || ''}
            </div>
          </div>
        ))}
      </div>

      <div className="card-footer">
        <div className={`card-rarity ${card.rarity === 'rare' ? 'rare' : card.rarity === 'ultra rare' ? 'ultra-rare' : ''}`}>
          {card.stage && `${card.stage} • `}
          {card.rarity || '普通'}
        </div>
      </div>

      <div className="card-actions">
        <button
          onClick={onCollect}
          className={isCollected ? 'collected-btn' : 'collect-btn'}
        >
          {isCollected ? '已收集 ✓' : '收集卡片'}
        </button>
      </div>
    </div>
  );
};

export default PokemonCard;