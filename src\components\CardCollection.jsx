import React, { createContext, useContext, useState, useEffect } from 'react';
import pokemonCards from '../pokemon-cards.json';

const CardCollectionContext = createContext();

export const CardCollectionProvider = ({ children }) => {
  const [collectedCards, setCollectedCards] = useState([]);
  const [drawnCards, setDrawnCards] = useState([]);
  const [collectionOpen, setCollectionOpen] = useState(false);

  // Load collected cards from localStorage on mount
  useEffect(() => {
    const savedCollected = localStorage.getItem('pokemonCollectedCards');
    const savedDrawn = localStorage.getItem('pokemonDrawnCards');
    if (savedCollected) setCollectedCards(JSON.parse(savedCollected));
    if (savedDrawn) setDrawnCards(JSON.parse(savedDrawn));
  }, []);

  // Save to localStorage whenever collection changes
  useEffect(() => {
    localStorage.setItem('pokemonCollectedCards', JSON.stringify(collectedCards));
    localStorage.setItem('pokemonDrawnCards', JSON.stringify(drawnCards));
  }, [collectedCards, drawnCards]);

  // Draw a random card from the deck
  const drawCard = () => {
    if (drawnCards.length >= pokemonCards.cards.length) {
      // Reset deck if all cards have been drawn
      setDrawnCards([]);
    }

    // Find available cards that haven't been drawn yet
    const availableCards = pokemonCards.cards.filter(
      card => !drawnCards.includes(card.id)
    );

    // Randomly select a card
    const randomIndex = Math.floor(Math.random() * availableCards.length);
    const selectedCard = availableCards[randomIndex];

    // Add to drawn cards
    setDrawnCards(prev => [...prev, selectedCard.id]);

    return selectedCard;
  };

  // Toggle collection status of a card
  const toggleCollection = (cardId) => {
    setCollectedCards(prev => 
      prev.includes(cardId)
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  // Get all collected cards
  const getCollectedCards = () => {
    return pokemonCards.cards.filter(card => collectedCards.includes(card.id));
  };

  return (
    <CardCollectionContext.Provider value={{
      drawCard,
      toggleCollection,
      getCollectedCards,
      collectedCards,
      collectionOpen,
      setCollectionOpen
    }}>
      {children}
    </CardCollectionContext.Provider>
  );
};

// Custom hook for using the collection context
export const useCardCollection = () => {
  const context = useContext(CardCollectionContext);
  if (!context) {
    throw new Error('useCardCollection must be used within a CardCollectionProvider');
  }
  return context;
};

// Collection view component
export const CardCollectionView = () => {
  const { getCollectedCards, toggleCollection, collectionOpen, setCollectionOpen } = useCardCollection();
  const collectedCards = getCollectedCards();

  if (!collectionOpen) return null;

  return (
    <div className="collection-modal">
      <div className="collection-header">
        <h2>我的宝可梦卡片收藏</h2>
        <button onClick={() => setCollectionOpen(false)} className="close-button">×</button>
      </div>
      <div className="collection-count">已收集 {collectedCards.length} 张卡片</div>
      <div className="collection-grid">
        {collectedCards.length > 0 ? (
          collectedCards.map(card => (
            <div key={card.id} className="collection-card">
              <div className="collection-card-image">
                {card.name}
              </div>
              <div className="collection-card-info">
                <h3>{card.name}</h3>
                <div>类型: {card.type}</div>
                <div>稀有度: {card.rarity}</div>
                <button 
                  onClick={() => toggleCollection(card.id)}
                  className="remove-collection-button"
                >
                  移除收藏
                </button>
              </div>
            </div>
          ))
        ) : (
          <div className="empty-collection">
            你的收藏还是空的，快去抽卡吧！
          </div>
        )}
      </div>
    </div>
  );
};

// Collection button component
export const CollectionButton = () => {
  const { setCollectionOpen } = useCardCollection();

  return (
    <button 
      className="collection-button"
      onClick={() => setCollectionOpen(true)}
    >
      查看收藏册
    </button>
  );
};

// Card pack component for drawing new cards
export const CardPack = () => {
  const { drawCard, toggleCollection } = useCardCollection();
  const [currentCard, setCurrentCard] = useState(null);
  const [isDrawing, setIsDrawing] = useState(false);

  const handleDrawCard = () => {
    setIsDrawing(true);
    // Simulate card drawing animation delay
    setTimeout(() => {
      const newCard = drawCard();
      setCurrentCard(newCard);
      setIsDrawing(false);
    }, 1500);
  };

  return (
    <div className="card-pack-container">
      <h3>宝可梦卡包</h3>
      <button 
        className="draw-card-button"
        onClick={handleDrawCard}
        disabled={isDrawing}
      >
        {isDrawing ? '正在抽卡...' : '抽取卡片'}
      </button>
      {currentCard && (
        <div className="drawn-card">
          <h4>你抽到了:</h4>
          <div className="card-preview">
            {currentCard.name} ({currentCard.type}类型)
          </div>
          <button 
            onClick={() => toggleCollection(currentCard.id)}
            className="collect-now-button"
          >
            收藏这张卡片
          </button>
        </div>
      )}
    </div>
  );
};

// Add collection styles
import '../App.css';

// This should be added to App.css but including here for completeness:
/*
.collection-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  padding: 20px;
  z-index: 1000;
}

.collection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  color: white;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
}

.collection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  overflow-y: auto;
  flex-grow: 1;
}

.collection-card {
  background-color: white;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.collection-card-image {
  width: 100px;
  height: 150px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  border-radius: 4px;
  font-weight: bold;
}

.collection-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 24px;
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.card-pack-container {
  background-color: var(--card-bg);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 30px;
  text-align: center;
}

.draw-card-button {
  padding: 15px 30px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  margin-top: 10px;
}

.drawn-card {
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
}

.collect-now-button {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: var(--success-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.remove-collection-button {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: var(--error-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
*/