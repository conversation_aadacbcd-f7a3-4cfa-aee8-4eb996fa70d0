import React from 'react';
import '../App.css';

const PokemonCard = ({ card, isCollected, onCollect }) => {
  // Determine card background color based on type
  const getTypeColor = (type) => {
    const typeColors = {
      '电': '#FFD700',
      '草': '#7CFC00',
      '水': '#1E90FF',
      '火': '#FF6347',
      '超能力': '#9932CC',
      '飞行': '#87CEEB',
      ' Psychic': '#9932CC',
      '格斗': '#CD5C5C',
      '岩石': '#A0522D',
      '地面': '#D2B48C',
      '钢': '#C0C0C0',
      '冰': '#87CEFA',
      '虫': '#7FFF00',
      '毒': '#9370DB',
      '幽灵': '#483D8B',
      '龙': '#4169E1',
      '恶': '#696969',
      '妖精': '#FF69B4'
    };
    return typeColors[type] || '#FFFFFF';
  };

  return (
    <div className="pokemon-card" style={{ border: `8px solid ${getTypeColor(card.type)}` }}>
      <div className="card-header">
        <div className="card-name">{card.name}</div>
        <div className="card-stage">{card.stage}</div>
      </div>
      <div className="card-image">
        {/* 在实际应用中，这里会显示宝可梦图片 */}
        <div className="placeholder-image" style={{ backgroundColor: '#f0f0f0', height: '180px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          {card.name} 图片
        </div>
      </div>
      <div className="card-stats">
        <div className="hp">HP {card.hp}</div>
        {card.evolvesFrom && (
          <div className="evolves-from"> evolves from {card.evolvesFrom}</div>
        )}
      </div>
      <div className="card-attacks">
        <h4>技能</h4>
        {card.attacks.map((attack, index) => (
          <div key={index} className="attack">
            <div className="attack-name">{attack.name}</div>
            <div className="attack-cost">{attack.cost.join(' ')}</div>
            <div className="attack-damage">{attack.damage} damage</div>
            {attack.text && <div className="attack-text">{attack.text}</div>}
          </div>
        ))}
      </div>
      <div className="card-weakness-resistance">
        {card.weaknesses && card.weaknesses.map((weakness, index) => (
          <div key={index} className="weakness">
            弱点: {weakness.type} {weakness.value}
          </div>
        ))}
        {card.resistances && card.resistances.map((resistance, index) => (
          <div key={index} className="resistance">
            抗性: {resistance.type} {resistance.value}
          </div>
        ))}
        <div className="retreat-cost">
          撤退费用: {card.retreatCost.join(' ')}
        </div>
      </div>
      <div className="card-description">
        {card.description}
      </div>
      <div className="card-rarity">
        稀有度: {card.rarity}
      </div>
      <button 
        className="collect-button"
        onClick={() => onCollect(card.id)}
        style={{ backgroundColor: isCollected ? '#4CAF50' : '#f44336', color: 'white', border: 'none', padding: '8px 16px', borderRadius: '4px', marginTop: '10px', cursor: 'pointer' }}
      >
        {isCollected ? '已收集' : '收集卡片'}
      </button>
    </div>
  );
};

export default PokemonCard;